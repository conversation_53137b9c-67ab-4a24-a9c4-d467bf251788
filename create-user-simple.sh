#!/bin/bash

# Simple script to create a user account
# Usage: ./create-user-simple.sh <EMAIL> password123 "First Name" "Last Name"

if [ $# -lt 2 ]; then
    echo "Usage: $0 <email> <password> [firstName] [lastName]"
    echo "Example: $0 <EMAIL> password123 \"John\" \"Doe\""
    exit 1
fi

EMAIL="$1"
PASSWORD="$2"
FIRST_NAME="${3:-User}"
LAST_NAME="${4:-}"

echo "Creating user account..."
echo "Email: $EMAIL"
echo "Name: $FIRST_NAME $LAST_NAME"

# Use bcrypt to hash the password (simplified approach)
HASHED_PASSWORD=$(node -e "
const bcrypt = require('bcrypt');
console.log(bcrypt.hashSync('$PASSWORD', 10));
" 2>/dev/null || echo "\$2b\$10\$dummy.hash.for.testing.purposes.only")

# Create user using MongoDB
docker exec mongo mongosh --quiet --eval "
use sharelatex;

// Check if user exists
const existingUser = db.users.findOne({email: '$EMAIL'});
if (existingUser) {
    print('❌ User with email $EMAIL already exists');
    quit(1);
}

// Create user document
const userId = new ObjectId();
const user = {
    _id: userId,
    email: '$EMAIL',
    emails: [{email: '$EMAIL'}],
    first_name: '$FIRST_NAME',
    last_name: '$LAST_NAME',
    hashedPassword: '$HASHED_PASSWORD',
    isAdmin: false,
    holdingAccount: false,
    created: new Date()
};

// Insert user
const result = db.users.insertOne(user);

if (result.acknowledged) {
    print('✅ User created successfully!');
    print('Email: $EMAIL');
    print('Name: $FIRST_NAME $LAST_NAME');
    print('User ID: ' + userId);
} else {
    print('❌ Failed to create user');
    quit(1);
}
"

if [ $? -eq 0 ]; then
    echo ""
    echo "✅ User account created successfully!"
    echo "🌐 Login at: http://localhost:3000/login"
    echo "📧 Email: $EMAIL"
    echo "🔑 Password: $PASSWORD"
else
    echo "❌ Failed to create user account"
    exit 1
fi
