// Simple script to create an admin user directly
const bcrypt = require('bcrypt');
const crypto = require('crypto');

// Generate a strong password
const generatePassword = (length = 10) => {
  return crypto.randomBytes(Math.ceil(length/2))
    .toString('hex')
    .slice(0, length);
};

// Admin details
const email = 'admin@localhost';
const password = generatePassword(12);
const passwordHash = bcrypt.hashSync(password, 10);

// MongoDB document
const adminUser = {
  email,
  emails: [{ email }],
  first_name: 'Admin',
  last_name: 'User',
  hashedPassword: passwordHash,
  isAdmin: true,
  created: new Date()
};

// Print info
console.log('Created admin user:');
console.log(`Email: ${email}`);
console.log(`Password: ${password}`);
console.log(`MongoDB Command:`);
console.log(JSON.stringify(adminUser, null, 2));

// Print the command to execute
console.log('\nTo create this user in MongoDB, run:');
console.log(`db.users.updateOne({email: '${email}'}, {$set: ${JSON.stringify({...adminUser, hashedPassword: passwordHash})}}, {upsert: true});`);
