@import (less) '../fonts/lato/lato.css';
@import (less) '../fonts/dm-mono/dm-mono.css';
@import (less) '../fonts/noto-sans/noto-sans.css';
@import (less) '../fonts/merriweather/merriweather.css';
@import (less) '../fonts/source-code-pro/source-code-pro.css';
@import (less) '../fonts/STIXTwoMath/stix-two-math.css';
@import (less) '../fonts/noto-serif/noto-serif.css';
@import (less) '../fonts/open-dyslexic-mono/open-dyslexic-mono.css';
@import (less) '../fonts/material-symbols/material-symbols.css';

@is-overleaf-light: false;

// Core variables and mixins
@import 'variables/all.less';
@import 'variables/css-variables.less';
@import 'app/ol-style-guide.less';
@import (less) '../fonts/font-awesome/font-awesome.css';

@import 'core/mixins.less';

// Reset
@import 'core/normalize.less';
@import 'core/print.less';

// Vendor CSS
@import (less) 'vendor/select/select.css';

// Core CSS
@import 'core/page.less';
@import 'core/scaffolding.less';
@import 'core/type.less';
@import 'core/grid.less';
@import 'core/accessibility.less';
@import 'core/spacing.less';
// Utility classes
@import 'core/utilities.less';
@import 'core/responsive-utilities.less';

// Components
@import 'components/tables.less';
@import 'components/forms.less';
@import 'components/badge.less';
@import 'components/buttons.less';
@import 'components/card.less';
@import 'components/component-animations.less';
@import 'components/dev-toolbar.less';
@import 'components/dropdowns.less';
@import 'components/button-groups.less';
@import 'components/input-groups.less';
@import 'components/navs.less';
@import 'components/navbar.less';
@import 'components/quote.less';
@import 'components/footer.less';
@import 'components/notifications.less';
@import 'components/labels.less';
@import 'components/loading-spinner';
@import 'components/thumbnails.less';
@import 'components/alerts.less';
@import 'components/progress-bars.less';
@import 'components/close.less';
@import 'components/hover.less';
@import 'components/ui-select.less';
@import 'components/input-suggestions.less';
@import 'components/nvd3.less';
@import 'components/nvd3_override.less';
@import 'components/infinite-scroll.less';
@import 'components/expand-collapse.less';
@import 'components/beta-badges.less';
@import 'components/divider.less';
@import 'components/input-switch.less';
@import 'components/container.less';
@import 'components/split-menu.less';
@import 'components/list-group.less';
@import 'components/select.less';
@import 'components/switch.less';
@import 'components/stepper.less';
@import 'components/radio-chip.less';
@import 'components/interstitial.less';

// Components w/ JavaScript
@import 'components/accordion.less';
@import 'components/modals.less';
@import 'components/tooltip.less';
@import 'components/popovers.less';
@import 'components/lists.less';
@import 'components/overbox.less';
@import 'components/embed-responsive.less';
@import 'components/icons.less';
@import 'components/images.less';
@import 'components/navs-ol.less';
@import 'components/pagination.less';
@import 'components/tabs.less';

// ngTagsInput
@import 'components/tags-input.less';

// Overleaf app classes
@import 'app/base.less';
@import 'app/beta-program.less';
@import 'app/about-page.less';
@import 'app/project-list.less';
@import 'app/project-list-react.less';
@import 'app/editor.less';
@import 'app/recurly.less';
@import 'app/bonus.less';
@import 'app/blog.less';
@import 'app/features.less';
@import 'app/templates.less';
@import 'app/wiki.less';
@import 'app/translations.less';
@import 'app/contact-us.less';
@import 'app/subscription.less';
@import 'app/change-plan-modal.less';
@import 'app/group-subscription-modal.less';
@import 'app/invite.less';
@import 'app/error-pages.less';
@import 'app/editor/history-v2.less';
@import 'app/open-in-overleaf.less';
@import 'app/grammarly';
@import 'app/front-chat-widget.less';
@import 'app/ol-chat.less';
@import 'app/templates-v2.less';
@import 'app/login-register.less';
@import 'app/import.less';
@import 'app/add-secondary-email-prompt.less';
@import 'app/confirm-email.less';

// Pages
@import 'app/about.less';
@import 'app/blog-posts.less';
@import 'app/cms-page.less';
@import 'app/content_page.less';
@import 'app/portals.less';

// module styles
// TODO: find a way for modules to add styles dynamically
@import 'modules/symbol-palette.less';
@import 'modules/git-bridge-modal.less';
@import 'modules/group-settings.less';
@import 'modules/onboarding.less';
@import 'modules/writefull.less';
@import 'modules/labs.less';
