volumes:
  clsi-cache:
  filestore-public-files:
  filestore-template-files:
  filestore-uploads:
  filestore-user-files:
  mongo-data:
  redis-data:
  sharelatex-data:
  web-data:
  history-v1-buckets:

services:
  chat:
    build:
      context: ..
      dockerfile: services/chat/Dockerfile
    env_file:
      - dev.env

  clsi:
    build:
      context: ..
      dockerfile: services/clsi/Dockerfile
    env_file:
      - dev.env
    environment:
      - TEXLIVE_IMAGE=texlive-full # docker build texlive -t texlive-full
      - SANDBOXED_COMPILES=true
      - SANDBOXED_COMPILES_HOST_DIR_COMPILES=${PWD}/compiles
      - SANDBOXED_COMPILES_HOST_DIR_OUTPUT=${PWD}/output
    user: root
    volumes:
      - ${PWD}/compiles:/overleaf/services/clsi/compiles
      - ${PWD}/output:/overleaf/services/clsi/output
      - ${DOCKER_SOCKET_PATH:-/var/run/docker.sock}:/var/run/docker.sock
      - clsi-cache:/overleaf/services/clsi/cache

  contacts:
    build:
      context: ..
      dockerfile: services/contacts/Dockerfile
    env_file:
      - dev.env

  docstore:
    build:
      context: ..
      dockerfile: services/docstore/Dockerfile
    env_file:
      - dev.env

  document-updater:
    build:
      context: ..
      dockerfile: services/document-updater/Dockerfile
    env_file:
      - dev.env

  filestore:
    build:
      context: ..
      dockerfile: services/filestore/Dockerfile
    env_file:
      - dev.env
#    environment:
#      - ENABLE_CONVERSIONS=true
    volumes:
      - filestore-public-files:/overleaf/services/filestore/public_files
      - filestore-template-files:/overleaf/services/filestore/template_files
      - filestore-uploads:/overleaf/services/filestore/uploads
      - filestore-user-files:/overleaf/services/filestore/user_files

  history-v1:
    build:
      context: ..
      dockerfile: services/history-v1/Dockerfile
    env_file:
        - dev.env
    environment:
      OVERLEAF_EDITOR_ANALYTICS_BUCKET: "/buckets/analytics"
      OVERLEAF_EDITOR_BLOBS_BUCKET: "/buckets/blobs"
      OVERLEAF_EDITOR_CHUNKS_BUCKET: "/buckets/chunks"
      OVERLEAF_EDITOR_PROJECT_BLOBS_BUCKET: "/buckets/project_blobs"
      OVERLEAF_EDITOR_ZIPS_BUCKET: "/buckets/zips"
      PERSISTOR_BACKEND: fs
    volumes:
        - history-v1-buckets:/buckets

  mongo:
    image: mongo:6.0
    command: --replSet overleaf
    ports:
      - "127.0.0.1:27017:27017" # for debugging
    volumes:
      - mongo-data:/data/db
      - ../bin/shared/mongodb-init-replica-set.js:/docker-entrypoint-initdb.d/mongodb-init-replica-set.js
    environment:
      MONGO_INITDB_DATABASE: sharelatex
    extra_hosts:
      # Required when using the automatic database setup for initializing the
      # replica set. This override is not needed when running the setup after
      # starting up mongo.
      - mongo:127.0.0.1

  notifications:
    build:
      context: ..
      dockerfile: services/notifications/Dockerfile
    env_file:
      - dev.env

  project-history:
    build:
      context: ..
      dockerfile: services/project-history/Dockerfile
    env_file:
        - dev.env

#  user-activate:
#    build:
#      context: ..
#      dockerfile: services/web/modules/user-activate/Dockerfile
#    env_file:
#      - dev.env

  real-time:
    build:
      context: ..
      dockerfile: services/real-time/Dockerfile
    env_file:
      - dev.env

  redis:
    image: redis:5
    ports:
      - "127.0.0.1:6379:6379" # for debugging
    volumes:
      - redis-data:/data

  web:
    build:
      context: ..
      dockerfile: services/web/Dockerfile
      target: dev
    env_file:
      - dev.env
    environment:
      - APP_NAME=Overleaf Community Edition
      - ENABLED_LINKED_FILE_TYPES=project_file,project_output_file
      - EMAIL_CONFIRMATION_DISABLED=true
      - NODE_ENV=development
      - OVERLEAF_ALLOW_PUBLIC_ACCESS=true
    command: ["node", "app.mjs"]
    volumes:
      - sharelatex-data:/var/lib/overleaf
      - web-data:/overleaf/services/web/data
    depends_on:
      - mongo
      - redis
      - chat
      - clsi
      - contacts
      - docstore
      - document-updater
      - filestore
      - history-v1
      - notifications
      - project-history
      - real-time

  webpack:
    build:
      context: ..
      dockerfile: services/web/Dockerfile
      target: webpack
    command: ["npx", "webpack", "serve", "--config", "webpack.config.dev-env.js"]
    ports:
      - "127.0.0.1:3000:3808"
    volumes:
      - ./webpack.config.dev-env.js:/overleaf/services/web/webpack.config.dev-env.js
