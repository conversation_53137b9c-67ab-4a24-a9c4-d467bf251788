#!/usr/bin/env node

// Simple script to create a new user account
// Usage: node create-user.js <EMAIL> password123 "First Name" "Last Name"

const { MongoClient } = require('mongodb');
const bcrypt = require('bcrypt');
const crypto = require('crypto');

// MongoDB connection
const MONGO_URL = 'mongodb://localhost:27017/sharelatex';

async function createUser(email, password, firstName, lastName) {
  const client = new MongoClient(MONGO_URL);
  
  try {
    await client.connect();
    const db = client.db('sharelatex');
    const users = db.collection('users');
    
    // Check if user already exists
    const existingUser = await users.findOne({ email: email });
    if (existingUser) {
      console.log(`❌ User with email ${email} already exists`);
      return;
    }
    
    // Hash password
    const hashedPassword = await bcrypt.hash(password, 10);
    
    // Create user document
    const user = {
      email: email,
      emails: [{ email: email }],
      first_name: firstName || 'User',
      last_name: lastName || '',
      hashedPassword: hashedPassword,
      isAdmin: false,
      holdingAccount: false,
      created: new Date(),
      _id: crypto.randomBytes(12)
    };
    
    // Insert user
    const result = await users.insertOne(user);
    
    console.log('✅ User created successfully!');
    console.log(`Email: ${email}`);
    console.log(`Name: ${firstName} ${lastName}`);
    console.log(`User ID: ${result.insertedId}`);
    
  } catch (error) {
    console.error('❌ Error creating user:', error.message);
  } finally {
    await client.close();
  }
}

// Parse command line arguments
const args = process.argv.slice(2);
if (args.length < 2) {
  console.log('Usage: node create-user.js <email> <password> [firstName] [lastName]');
  console.log('Example: node create-user.js <EMAIL> password123 "John" "Doe"');
  process.exit(1);
}

const [email, password, firstName, lastName] = args;

// Validate email
if (!email.includes('@')) {
  console.error('❌ Invalid email address');
  process.exit(1);
}

// Validate password
if (password.length < 6) {
  console.error('❌ Password must be at least 6 characters long');
  process.exit(1);
}

createUser(email, password, firstName, lastName);
