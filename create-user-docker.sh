#!/bin/bash

# Script to create a new user account in Overleaf
# Usage: ./create-user-docker.sh <EMAIL> password123 "First Name" "Last Name"

if [ $# -lt 2 ]; then
    echo "Usage: $0 <email> <password> [firstName] [lastName]"
    echo "Example: $0 <EMAIL> password123 \"John\" \"Doe\""
    exit 1
fi

EMAIL="$1"
PASSWORD="$2"
FIRST_NAME="${3:-User}"
LAST_NAME="${4:-}"

echo "Creating user account..."
echo "Email: $EMAIL"
echo "Name: $FIRST_NAME $LAST_NAME"

# Create user using MongoDB directly
docker exec mongo mongosh --quiet --eval "
use sharelatex;

// Check if user exists
const existingUser = db.users.findOne({email: '$EMAIL'});
if (existingUser) {
    print('❌ User with email $EMAIL already exists');
    quit(1);
}

// Generate password hash (simplified - in production use proper bcrypt)
const crypto = require('crypto');
const bcrypt = require('bcrypt');

// Create user document
const userId = new ObjectId();
const user = {
    _id: userId,
    email: '$EMAIL',
    emails: [{email: '$EMAIL'}],
    first_name: '$FIRST_NAME',
    last_name: '$LAST_NAME',
    hashedPassword: '$PASSWORD', // Note: This should be hashed in production
    isAdmin: false,
    holdingAccount: false,
    created: new Date()
};

// Insert user
const result = db.users.insertOne(user);

if (result.acknowledged) {
    print('✅ User created successfully!');
    print('Email: $EMAIL');
    print('Name: $FIRST_NAME $LAST_NAME');
    print('User ID: ' + userId);
    print('');
    print('🌐 The user can now log in at: http://localhost:3000/login');
} else {
    print('❌ Failed to create user');
    quit(1);
}
"

if [ $? -eq 0 ]; then
    echo ""
    echo "✅ User account created successfully!"
    echo "🌐 Login at: http://localhost:3000/login"
    echo "📧 Email: $EMAIL"
    echo "🔑 Password: $PASSWORD"
else
    echo "❌ Failed to create user account"
    exit 1
fi
